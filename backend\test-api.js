// Test script för LLM provider API
require('dotenv').config();

const BASE_URL = 'http://localhost:3002/api/settings';

async function testAPI() {
  console.log('🧪 Testing LLM Provider API...');

  try {
    // Test 1: Get current provider
    console.log('\n1. Getting current provider configuration...');
    const getCurrentResponse = await fetch(`${BASE_URL}/llm-provider`);
    const currentConfig = await getCurrentResponse.json();
    console.log('Current config:', JSON.stringify(currentConfig, null, 2));

    // Test 2: Test current provider connection
    console.log('\n2. Testing current provider connection...');
    const testResponse = await fetch(`${BASE_URL}/llm-provider/test`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    const testResult = await testResponse.json();
    console.log('Test result:', JSON.stringify(testResult, null, 2));

    // Test 3: Switch to Azure provider
    console.log('\n3. Switching to Azure provider...');
    const switchToAzureResponse = await fetch(`${BASE_URL}/llm-provider`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        provider: 'azure',
        defaultModel: 'gpt-4o-mini'
      })
    });
    const azureSwitchResult = await switchToAzureResponse.json();
    console.log('Azure switch result:', JSON.stringify(azureSwitchResult, null, 2));

    // Test 4: Test Azure provider connection
    if (azureSwitchResult.success) {
      console.log('\n4. Testing Azure provider connection...');
      const azureTestResponse = await fetch(`${BASE_URL}/llm-provider/test`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const azureTestResult = await azureTestResponse.json();
      console.log('Azure test result:', JSON.stringify(azureTestResult, null, 2));
    }

    // Test 5: Switch back to OpenAI provider
    console.log('\n5. Switching back to OpenAI provider...');
    const switchToOpenAIResponse = await fetch(`${BASE_URL}/llm-provider`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        provider: 'openai',
        defaultModel: 'gpt-4o-mini'
      })
    });
    const openaiSwitchResult = await switchToOpenAIResponse.json();
    console.log('OpenAI switch result:', JSON.stringify(openaiSwitchResult, null, 2));

    // Test 6: Final test of OpenAI provider
    if (openaiSwitchResult.success) {
      console.log('\n6. Final test of OpenAI provider...');
      const finalTestResponse = await fetch(`${BASE_URL}/llm-provider/test`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const finalTestResult = await finalTestResponse.json();
      console.log('Final test result:', JSON.stringify(finalTestResult, null, 2));
    }

    console.log('\n✅ API tests completed!');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Wait a bit for server to start, then run tests
setTimeout(testAPI, 3000);
