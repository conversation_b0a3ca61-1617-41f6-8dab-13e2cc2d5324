import { useState, useEffect } from 'react'
import { settingsApi } from '../services/api'

export function Settings() {
  const [masterKey, setMasterKey] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [masterKeyExists, setMasterKeyExists] = useState<boolean | null>(null)
  const [llmProvider, setLlmProvider] = useState('')
  const [llmDefaultModel, setLlmDefaultModel] = useState('')
  const [availableProviders, setAvailableProviders] = useState<string[]>([])
  const [llmLoading, setLlmLoading] = useState(false)
  const [llmMessage, setLlmMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [testingConnection, setTestingConnection] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // Load master key status
      const masterKeyResponse = await settingsApi.getMasterKeyStatus()
      if (masterKeyResponse.success && masterKeyResponse.data) {
        setMasterKeyExists(masterKeyResponse.data.exists)
      }

      // Load LLM provider configuration
      const llmResponse = await settingsApi.getLLMProviderConfig()
      if (llmResponse.success && llmResponse.data) {
        setLlmProvider(llmResponse.data.provider)
        setLlmDefaultModel(llmResponse.data.defaultModel)
        setAvailableProviders(llmResponse.data.availableProviders)
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const handleUpdateMasterKey = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!masterKey.trim()) {
      setMessage({ type: 'error', text: 'Huvudnyckel kan inte vara tom' })
      return
    }

    if (masterKey.length !== 64) {
      setMessage({ type: 'error', text: 'Huvudnyckel måste vara exakt 64 hex-tecken (32 bytes)' })
      return
    }

    if (!/^[0-9a-fA-F]+$/.test(masterKey)) {
      setMessage({ type: 'error', text: 'Huvudnyckel får endast innehålla hexadecimala tecken (0-9, a-f, A-F)' })
      return
    }

    try {
      setLoading(true)
      setMessage(null)

      const result = await settingsApi.updateMasterKey(masterKey)

      if (result.success) {
        setMessage({ type: 'success', text: 'Huvudnyckel uppdaterad framgångsrikt' })
        setMasterKey('')
        setMasterKeyExists(true)
      } else {
        setMessage({ type: 'error', text: result.error || 'Misslyckades att uppdatera huvudnyckel' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Misslyckades att uppdatera huvudnyckel' })
      console.error('Error updating master key:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateRandomKey = () => {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
    setMasterKey(hex.toUpperCase())
  }

  const handleUpdateLLMProvider = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setLlmLoading(true)
      setLlmMessage(null)

      const result = await settingsApi.updateLLMProviderConfig(llmProvider, llmDefaultModel)

      if (result.success) {
        setLlmMessage({
          type: 'success',
          text: `LLM provider har bytts till ${llmProvider === 'openai' ? 'OpenAI' : 'Azure OpenAI'}. Anslutningen testades och fungerar.`
        })
        // Reload settings to get updated configuration
        await loadSettings()
      } else {
        setLlmMessage({ type: 'error', text: result.error || 'Misslyckades att uppdatera LLM provider-konfiguration' })
      }
    } catch (error) {
      setLlmMessage({ type: 'error', text: 'Misslyckades att uppdatera LLM provider-konfiguration' })
      console.error('Error updating LLM provider:', error)
    } finally {
      setLlmLoading(false)
    }
  }

  const handleTestConnection = async () => {
    try {
      setTestingConnection(true)
      setLlmMessage(null)

      const result = await settingsApi.testLLMProviderConnection()

      if (result.success && result.data) {
        setLlmMessage({
          type: 'success',
          text: `Anslutning till ${result.data.provider === 'openai' ? 'OpenAI' : 'Azure OpenAI'} fungerar. Stödda modeller: ${result.data.supportedModels?.join(', ')}`
        })
      } else {
        setLlmMessage({
          type: 'error',
          text: result.data?.message || result.error || 'Anslutningstest misslyckades'
        })
      }
    } catch (error) {
      setLlmMessage({ type: 'error', text: 'Misslyckades att testa anslutning' })
      console.error('Error testing connection:', error)
    } finally {
      setTestingConnection(false)
    }
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Inställningar</p>
          <p className="dashboard-subtitle">
            Konfigurera applikationsinställningar och säkerhet.
          </p>
        </div>
      </div>

      {/* Master Key Section */}
      <h2 className="section-title">Huvudnyckel-konfiguration</h2>
      <div className="table-container">
        <div className="activity-table">
          <div style={{ padding: '1.5rem' }}>
            {masterKeyExists === null ? (
              <p style={{ margin: '0 0 1.5rem 0', color: '#6b7280', fontSize: '0.875rem' }}>
                Laddar huvudnyckel-status...
              </p>
            ) : masterKeyExists ? (
              <div>
                <p style={{ margin: '0 0 1.5rem 0', color: '#059669', fontSize: '0.875rem', fontWeight: '500' }}>
                  ✅ Huvudnyckel är redan konfigurerad
                </p>
                <p style={{ margin: '0', color: '#6b7280', fontSize: '0.875rem' }}>
                  Huvudnyckeln används för att kryptera och dekryptera lagrade inloggningsuppgifter.
                  Den är redan konfigurerad och fungerar.
                </p>
              </div>
            ) : (
              <p style={{ margin: '0 0 1.5rem 0', color: '#6b7280', fontSize: '0.875rem' }}>
                Huvudnyckeln används för att kryptera och dekryptera lagrade inloggningsuppgifter.
                Den måste vara exakt 32 bytes (64 hexadecimala tecken).
              </p>
            )}

            {!masterKeyExists && (
              <>
                {/* Warning */}
                <div style={{
                  padding: '1rem',
                  backgroundColor: '#fef3c7',
                  border: '1px solid #f59e0b',
                  borderRadius: '0.375rem',
                  marginBottom: '1.5rem'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                    <span style={{ fontSize: '1.25rem' }}>⚠️</span>
                    <strong style={{ color: '#92400e' }}>Viktigt</strong>
                  </div>
                  <p style={{ margin: 0, color: '#92400e', fontSize: '0.875rem' }}>
                    Att ändra huvudnyckeln kommer att göra alla befintliga inloggningsuppgifter otillgängliga.
                    Ändra endast detta om du vet vad du gör.
                  </p>
                </div>

                {/* Message */}
                {message && (
                  <div style={{
                    padding: '0.75rem',
                    backgroundColor: message.type === 'success' ? '#f0fdf4' : '#fef2f2',
                    border: `1px solid ${message.type === 'success' ? '#bbf7d0' : '#fecaca'}`,
                    borderRadius: '0.375rem',
                    color: message.type === 'success' ? '#166534' : '#dc2626',
                    marginBottom: '1.5rem',
                    fontSize: '0.875rem'
                  }}>
                    {message.text}
                  </div>
                )}

                <form onSubmit={handleUpdateMasterKey}>
              <div className="form-group">
                <label className="form-label">
                  Huvudnyckel (64 hex-tecken)
                </label>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <input
                    type="text"
                    value={masterKey}
                    onChange={(e) => setMasterKey(e.target.value.toUpperCase())}
                    className="form-input"
                    style={{ flex: 1, fontFamily: 'monospace' }}
                    placeholder="Ange 64 hex-tecken (t.ex. A1B2C3D4...)"
                    maxLength={64}
                  />
                  <button
                    type="button"
                    onClick={generateRandomKey}
                    className="action-button secondary"
                  >
                    <span>Generera</span>
                  </button>
                </div>
                <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  {masterKey.length}/64 tecken
                </div>
              </div>

              <button
                type="submit"
                disabled={loading || masterKey.length !== 64}
                className={`action-button ${loading || masterKey.length !== 64 ? 'secondary' : 'danger'}`}
                style={{ cursor: loading || masterKey.length !== 64 ? 'not-allowed' : 'pointer' }}
              >
                <span>{loading ? 'Uppdaterar...' : 'Uppdatera huvudnyckel'}</span>
              </button>
            </form>
              </>
            )}
          </div>
        </div>
      </div>

      {/* LLM Provider Settings */}
      <h2 className="section-title">LLM Provider-konfiguration</h2>
      <div className="table-container">
        <div className="activity-table">
          <div style={{ padding: '1.5rem' }}>
            <p style={{ margin: '0 0 1rem 0', color: '#6b7280', fontSize: '0.875rem' }}>
              Välj vilken LLM provider som ska användas för AI-assistenten och andra AI-funktioner.
              Bytet sker direkt utan att behöva starta om applikationen.
            </p>

            {/* Current Provider Status */}
            {llmProvider && (
              <div style={{
                padding: '0.75rem',
                backgroundColor: '#f8fafc',
                border: '1px solid #e2e8f0',
                borderRadius: '0.375rem',
                marginBottom: '1.5rem',
                fontSize: '0.875rem'
              }}>
                <strong>Aktuell provider:</strong> {llmProvider === 'openai' ? 'OpenAI' : 'Azure OpenAI'}
                {llmDefaultModel && <span> | <strong>Modell:</strong> {llmDefaultModel}</span>}
              </div>
            )}

            {/* LLM Message */}
            {llmMessage && (
              <div style={{
                padding: '0.75rem',
                backgroundColor: llmMessage.type === 'success' ? '#f0fdf4' : '#fef2f2',
                border: `1px solid ${llmMessage.type === 'success' ? '#bbf7d0' : '#fecaca'}`,
                borderRadius: '0.375rem',
                color: llmMessage.type === 'success' ? '#166534' : '#dc2626',
                marginBottom: '1.5rem',
                fontSize: '0.875rem'
              }}>
                {llmMessage.text}
              </div>
            )}

            <form onSubmit={handleUpdateLLMProvider}>
              <div className="form-group">
                <label className="form-label">
                  LLM Provider
                </label>
                <select
                  value={llmProvider}
                  onChange={(e) => setLlmProvider(e.target.value)}
                  className="form-input"
                  style={{ marginBottom: '1rem' }}
                >
                  {availableProviders.map(provider => (
                    <option key={provider} value={provider}>
                      {provider === 'openai' ? 'OpenAI' : provider === 'azure' ? 'Azure OpenAI' : provider}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">
                  Standardmodell
                </label>
                <input
                  type="text"
                  value={llmDefaultModel}
                  onChange={(e) => setLlmDefaultModel(e.target.value)}
                  className="form-input"
                  placeholder="t.ex. gpt-4o-mini"
                  style={{ marginBottom: '1rem' }}
                />
                <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Lämna tom för att använda standardvärdet
                </div>
              </div>

              <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
                <button
                  type="submit"
                  disabled={llmLoading || !llmProvider}
                  className={`action-button ${llmLoading || !llmProvider ? 'secondary' : 'primary'}`}
                  style={{ cursor: llmLoading || !llmProvider ? 'not-allowed' : 'pointer' }}
                >
                  <span>{llmLoading ? 'Byter provider...' : 'Byt LLM Provider'}</span>
                </button>

                <button
                  type="button"
                  onClick={handleTestConnection}
                  disabled={testingConnection || !llmProvider}
                  className={`action-button ${testingConnection || !llmProvider ? 'secondary' : 'primary'}`}
                  style={{
                    cursor: testingConnection || !llmProvider ? 'not-allowed' : 'pointer',
                    backgroundColor: testingConnection || !llmProvider ? undefined : '#059669'
                  }}
                >
                  <span>{testingConnection ? 'Testar anslutning...' : 'Testa anslutning'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Other Settings */}
      <h2 className="section-title">Övriga inställningar</h2>
      <div className="table-container">
        <div className="activity-table">
          <div style={{ padding: '1.5rem' }}>
            <p style={{ margin: 0, color: '#6b7280', fontSize: '0.875rem' }}>
              Ytterligare applikationsinställningar kommer att finnas tillgängliga här i framtida uppdateringar.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
